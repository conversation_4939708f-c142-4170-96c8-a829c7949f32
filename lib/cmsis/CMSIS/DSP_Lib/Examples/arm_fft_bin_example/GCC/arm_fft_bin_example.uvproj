<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>ARMCM0</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x00000000,0x40000) IRAM(0x20000000,0x20000) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL040000 -FP0($$Device:ARMCM0$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0$Device\ARM\SVD\ARMCM0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\ARMCM0_debug\</OutputDirectory>
          <OutputName>arm_fft_bin_example</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\ARMCM0_debug\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
            <UsePdscDebugDescription>1</UsePdscDebugDescription>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\arm_fft_bin_example.ini</InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\UL2CM3.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>0</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M0"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>0</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>0</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>1</Optim>
            <wLevel>2</wLevel>
            <uThumb>1</uThumb>
            <VariousControls>
              <MiscControls>-fno-strict-aliasing -ffunction-sections -fdata-sections</MiscControls>
              <Define>ARM_MATH_CM0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\..\Include</IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>0</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>0</noStart>
            <noStLib>0</noStLib>
            <uMathLib>0</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs>arm_cortexM0l_math</IncludeLibs>
            <IncludeDir>..\..\..\..\Lib\GCC</IncludeDir>
            <Misc>-Wl,--gc-sections</Misc>
            <ScatterFile>.\Startup\ARMCMx.ld</ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source Files</GroupName>
          <Files>
            <File>
              <FileName>arm_fft_bin_example_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_example_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fft_bin_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_data.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS Device</GroupName>
          <Files>
            <File>
              <FileName>startup_ARMCM0.S</FileName>
              <FileType>1</FileType>
              <FilePath>./Startup/startup_ARMCM0.S</FilePath>
            </File>
            <File>
              <FileName>system_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM0.c</FilePath>
            </File>
            <File>
              <FileName>startup_ARMCM3.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM3.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM3.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM3.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>startup_ARMCM4.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM4.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM4.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM4.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS DSP_Library</GroupName>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>ARMCM3</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM3</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x00000000,0x40000) IRAM(0x20000000,0x20000) CPUTYPE("Cortex-M3") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL040000 -FP0($$Device:ARMCM3$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM3$Device\ARM\ARMCM3\Include\ARMCM3.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM3$Device\ARM\SVD\ARMCM3.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\ARMCM3_debug\</OutputDirectory>
          <OutputName>arm_fft_bin_example</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\ARMCM3_debug\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
            <UsePdscDebugDescription>1</UsePdscDebugDescription>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\arm_fft_bin_example.ini</InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\UL2CM3.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>0</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M3"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>0</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>0</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>1</Optim>
            <wLevel>2</wLevel>
            <uThumb>1</uThumb>
            <VariousControls>
              <MiscControls>-fno-strict-aliasing -ffunction-sections -fdata-sections</MiscControls>
              <Define>ARM_MATH_CM3</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\..\Include</IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>0</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>0</noStart>
            <noStLib>0</noStLib>
            <uMathLib>0</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs>arm_cortexM3l_math</IncludeLibs>
            <IncludeDir>..\..\..\..\Lib\GCC</IncludeDir>
            <Misc>-Wl,--gc-sections</Misc>
            <ScatterFile>.\Startup\ARMCMx.ld</ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source Files</GroupName>
          <Files>
            <File>
              <FileName>arm_fft_bin_example_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_example_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fft_bin_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_data.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS Device</GroupName>
          <Files>
            <File>
              <FileName>startup_ARMCM0.S</FileName>
              <FileType>1</FileType>
              <FilePath>./Startup/startup_ARMCM0.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM0.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>startup_ARMCM3.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM3.S</FilePath>
            </File>
            <File>
              <FileName>system_ARMCM3.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM3.c</FilePath>
            </File>
            <File>
              <FileName>startup_ARMCM4.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM4.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM4.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM4.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS DSP_Library</GroupName>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>ARMCM4_FP</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM4_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x00000000,0x80000) IRAM(0x20000000,0x20000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL080000 -FP0($$Device:ARMCM4_FP$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM4_FP$Device\ARM\ARMCM4\Include\ARMCM4_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM4_FP$Device\ARM\SVD\ARMCM4.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\ARMCM4_debug\</OutputDirectory>
          <OutputName>arm_fft_bin_example</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\ARMCM4_debug\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
            <UsePdscDebugDescription>1</UsePdscDebugDescription>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\arm_fft_bin_example.ini</InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\UL2CM3.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>0</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M4"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>0</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>0</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>1</Optim>
            <wLevel>2</wLevel>
            <uThumb>1</uThumb>
            <VariousControls>
              <MiscControls>-fno-strict-aliasing -ffunction-sections -fdata-sections -mfpu=fpv4-sp-d16 -mfloat-abi=hard</MiscControls>
              <Define>ARM_MATH_CM4, __FPU_PRESENT = 1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\..\Include</IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>0</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>0</noStart>
            <noStLib>0</noStLib>
            <uMathLib>0</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs>arm_cortexM4lf_math</IncludeLibs>
            <IncludeDir>..\..\..\..\Lib\GCC</IncludeDir>
            <Misc>-Wl,--gc-sections -mfpu=fpv4-sp-d16 -mfloat-abi=hard</Misc>
            <ScatterFile>.\Startup\ARMCMx.ld</ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source Files</GroupName>
          <Files>
            <File>
              <FileName>arm_fft_bin_example_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_example_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_fft_bin_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\arm_fft_bin_data.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS Device</GroupName>
          <Files>
            <File>
              <FileName>startup_ARMCM0.S</FileName>
              <FileType>1</FileType>
              <FilePath>./Startup/startup_ARMCM0.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM0.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>startup_ARMCM3.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM3.S</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>system_ARMCM3.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM3.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArm>
                  <Carm>
                    <arpcs>2</arpcs>
                    <stkchk>2</stkchk>
                    <reentr>2</reentr>
                    <interw>2</interw>
                    <bigend>2</bigend>
                    <Strict>0</Strict>
                    <Optim>0</Optim>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Carm>
                </FileArm>
              </FileOption>
            </File>
            <File>
              <FileName>startup_ARMCM4.S</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\startup_ARMCM4.S</FilePath>
            </File>
            <File>
              <FileName>system_ARMCM4.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Startup\system_ARMCM4.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS DSP_Library</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
