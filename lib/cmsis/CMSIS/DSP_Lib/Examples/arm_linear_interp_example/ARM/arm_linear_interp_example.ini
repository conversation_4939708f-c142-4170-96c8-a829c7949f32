
/* This file demonstrates how to Map memory ranges, specify read, write, and execute permissions

   The file can be executed in the following way:
   1) manually from uVision command window (in debug mode) using command:
   INCLUIDE arm_linear_interp_example.ini

*/


// usual initialisation for target setup
MAP  0x00000000, 0x0003FFFF  EXEC READ      // 256K Flash
MAP  0x20000000, 0x20007FFF  READ WRITE     //  32K RAM


