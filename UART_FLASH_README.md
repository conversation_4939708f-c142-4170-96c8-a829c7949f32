# UART Binary Flashing Support

This document describes the UART binary flashing feature added to the UF2 bootloader for SAMD microcontrollers.

## Overview

The UART binary flashing feature allows firmware updates to be performed over a serial UART connection using plain binary files instead of USB. This is particularly useful for:

- Devices without USB connectivity
- Remote firmware updates over serial links
- Automated testing and deployment scenarios
- Recovery when USB is not functional
- Simple binary firmware deployment without UF2 conversion

## Protocol Description

The UART binary flash protocol uses a simple framed format to transmit raw binary firmware data over UART:

### Frame Structure

```
Header (16 bytes):
+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
| MAGIC_START (4) | CMD(1) | SEQ(2) | ADDR(4) | LEN(2) | TOTAL_SIZE(4) | CHECKSUM(1) |
+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+

Data (256 bytes):
+--------+--------+--------+--------+
|    Binary Data (256 bytes)      |
+--------+--------+--------+--------+

Footer (4 bytes):
+--------+--------+--------+--------+
| MAGIC_END (4) |
+--------+--------+--------+--------+
```

### Commands

- `UART_FLASH_CMD_START (0x01)`: Start flashing session with total size
- `UART_FLASH_CMD_DATA (0x02)`: Send binary data block
- `UART_FLASH_CMD_FINISH (0x03)`: Finish flashing session
- `UART_FLASH_CMD_RESET (0x04)`: Reset device after flashing

### Response Codes

- `UART_FLASH_RESP_OK (0x06)`: ACK - Success
- `UART_FLASH_RESP_ERROR (0x15)`: NAK - Error
- `UART_FLASH_RESP_READY (0x52)`: Ready for next block

## Configuration

### Enabling UART Binary Flashing Support

To enable UART binary flashing, set the following in your board configuration or `inc/uf2.h`:

```c
#define USE_UART 1
#define USE_UART_FLASH 1
```

### Configuration Options

The following options can be configured in `board_config.h`:

```c
// Size of receive buffer (default: 512 bytes)
#define UART_FLASH_BUFFER_SIZE   512

// Maximum firmware size (default: 256KB)
#define UART_FLASH_MAX_SIZE      (256*1024)
```

## Usage

### Bootloader Behavior

1. The bootloader initializes UART and UART flash subsystem on startup
2. In the main loop, it checks for incoming UART data
3. If binary flash protocol data is detected, it enters binary flashing mode
4. If a '#' character is received, it falls back to SAM-BA monitor mode
5. LED color changes to indicate UART activity (COLOR_UART)

### Flashing Sequence

1. **Start**: Send START command with total firmware size
2. **Transfer**: Send DATA commands with sequential 256-byte chunks of binary firmware
3. **Finish**: Send FINISH command to complete flashing
4. **Reset**: Device automatically resets into application

### Error Handling

- Simple XOR checksums validate frame headers
- Sequence numbers prevent duplicate/missing blocks
- Address validation ensures sequential writing
- Timeouts handle communication failures
- Retransmission is supported for reliability

## Implementation Details

### Files Added

- `inc/uart_flash.h`: Protocol definitions and function prototypes
- `src/uart_flash.c`: Implementation of UART binary flash protocol
- `UART_FLASH_README.md`: This documentation
- `test_uart_flash.py`: Python test client for binary flashing

### Files Modified

- `inc/uf2.h`: Added configuration options and version string
- `src/main.c`: Integrated UART binary flash processing into main loop

### Memory Usage

The UART binary flash implementation adds approximately:
- 1.5KB of flash memory for code
- 512 bytes of RAM for buffers and state
- Configurable receive buffer size

## Testing

### Basic Functionality Test

1. Build bootloader with UART binary flash enabled
2. Connect UART to development board
3. Send binary firmware file using protocol-compliant client
4. Verify firmware is flashed and device resets

### Compatibility Test

1. Verify SAM-BA monitor still works (send '#' character)
2. Verify USB MSC/HID flashing still works
3. Test with various binary firmware sizes
4. Test error conditions and recovery

## Limitations

- Requires UART hardware support on target board
- Slower than USB-based flashing
- No built-in flow control (relies on protocol-level ACK/NAK)
- Single-threaded implementation (blocks other operations during flashing)
- Works with binary files only (no UF2 format support over UART)

## Future Enhancements

- Flow control support (RTS/CTS)
- Compression support for faster transfers
- Multi-block transfers in single frame
- Progress reporting
- Resume capability for interrupted transfers
- Optional UF2 format support alongside binary