#!/usr/bin/env python3
"""
UART UF2 Flashing Test Client

This script demonstrates how to flash UF2 files over UART using the
UART UF2 protocol implemented in the bootloader.

Usage:
    python3 test_uart_uf2.py <serial_port> <uf2_file>

Example:
    python3 test_uart_uf2.py /dev/ttyUSB0 firmware.uf2
    python3 test_uart_uf2.py COM3 firmware.uf2
"""

import sys
import time
import struct
import serial
import argparse
from pathlib import Path

# Protocol constants
UF2_UART_MAGIC_START = 0x55464232  # "UF2\x32"
UF2_UART_MAGIC_END = 0x454E4432    # "END\x32"
UF2_UART_BLOCK_SIZE = 512
UF2_UART_FRAME_SIZE = 532  # 12 + 512 + 8

# Commands
UF2_UART_CMD_INIT = 0x01
UF2_UART_CMD_BLOCK = 0x02
UF2_UART_CMD_FINISH = 0x03
UF2_UART_CMD_RESET = 0x04

# Response codes
UF2_UART_RESP_OK = 0x06
UF2_UART_RESP_ERROR = 0x15
UF2_UART_RESP_READY = 0x52
UF2_UART_RESP_BUSY = 0x42

# Error codes
UF2_UART_ERR_NONE = 0x00
UF2_UART_ERR_CHECKSUM = 0x01
UF2_UART_ERR_INVALID = 0x02
UF2_UART_ERR_ADDRESS = 0x03
UF2_UART_ERR_SEQUENCE = 0x04
UF2_UART_ERR_TIMEOUT = 0x05

def crc16(data):
    """Calculate CRC16 checksum"""
    crc = 0xFFFF
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 1:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc & 0xFFFF

def crc32(data):
    """Calculate CRC32 checksum"""
    import zlib
    return zlib.crc32(data) & 0xFFFFFFFF

def create_frame(command, sequence, data):
    """Create a UART UF2 protocol frame"""
    # Ensure data is exactly 512 bytes
    if len(data) < UF2_UART_BLOCK_SIZE:
        data = data + b'\x00' * (UF2_UART_BLOCK_SIZE - len(data))
    elif len(data) > UF2_UART_BLOCK_SIZE:
        data = data[:UF2_UART_BLOCK_SIZE]

    # Create header (without checksum)
    header_data = struct.pack('<LBHHB',
                             UF2_UART_MAGIC_START,
                             command,
                             sequence,
                             UF2_UART_BLOCK_SIZE,
                             0)  # reserved

    # Calculate header checksum
    header_crc = crc16(header_data)

    # Complete header with checksum
    header = header_data + struct.pack('<H', header_crc)

    # Calculate data checksum
    data_crc = crc32(data)

    # Create footer
    footer = struct.pack('<LL', data_crc, UF2_UART_MAGIC_END)

    # Combine frame
    frame = header + data + footer
    return frame

def read_response(ser, timeout=5.0):
    """Read response from bootloader"""
    start_time = time.time()
    response_data = b''

    while len(response_data) < 4 and (time.time() - start_time) < timeout:
        if ser.in_waiting > 0:
            response_data += ser.read(ser.in_waiting)
        time.sleep(0.01)

    if len(response_data) >= 4:
        response, error_code, sequence = struct.unpack('<BBH', response_data[:4])
        return response, error_code, sequence

    return None, None, None

def flash_uf2_file(port, uf2_file, baud_rate=115200):
    """Flash UF2 file over UART"""
    print(f"Opening {port} at {baud_rate} baud...")

    try:
        ser = serial.Serial(port, baud_rate, timeout=1.0)
        time.sleep(0.1)  # Allow port to stabilize
    except Exception as e:
        print(f"Error opening serial port: {e}")
        return False

    try:
        # Read UF2 file
        print(f"Reading UF2 file: {uf2_file}")
        with open(uf2_file, 'rb') as f:
            uf2_data = f.read()

        if len(uf2_data) % 512 != 0:
            print("Error: UF2 file size is not a multiple of 512 bytes")
            return False

        num_blocks = len(uf2_data) // 512
        print(f"UF2 file contains {num_blocks} blocks")

        # Send initialization command
        print("Sending initialization command...")
        init_frame = create_frame(UF2_UART_CMD_INIT, 0, uf2_data[:512])
        ser.write(init_frame)

        response, error_code, sequence = read_response(ser)
        if response != UF2_UART_RESP_OK:
            print(f"Initialization failed: response={response}, error={error_code}")
            return False

        print("Initialization successful")

        # Send all blocks
        for i in range(num_blocks):
            block_data = uf2_data[i*512:(i+1)*512]
            print(f"Sending block {i+1}/{num_blocks}...")

            block_frame = create_frame(UF2_UART_CMD_BLOCK, i+1, block_data)
            ser.write(block_frame)

            response, error_code, sequence = read_response(ser)
            if response != UF2_UART_RESP_OK:
                print(f"Block {i+1} failed: response={response}, error={error_code}")
                return False

            # Show progress
            if (i + 1) % 10 == 0 or i == num_blocks - 1:
                progress = ((i + 1) * 100) // num_blocks
                print(f"Progress: {progress}% ({i+1}/{num_blocks} blocks)")

        # Send finish command
        print("Sending finish command...")
        finish_frame = create_frame(UF2_UART_CMD_FINISH, num_blocks + 1, b'\x00' * 512)
        ser.write(finish_frame)

        response, error_code, sequence = read_response(ser)
        if response != UF2_UART_RESP_OK:
            print(f"Finish failed: response={response}, error={error_code}")
            return False

        print("Flashing completed successfully!")
        print("Device should reset into the new application...")

        return True

    except Exception as e:
        print(f"Error during flashing: {e}")
        return False

    finally:
        ser.close()

def main():
    parser = argparse.ArgumentParser(description='Flash UF2 file over UART')
    parser.add_argument('port', help='Serial port (e.g., /dev/ttyUSB0, COM3)')
    parser.add_argument('uf2_file', help='UF2 file to flash')
    parser.add_argument('--baud', type=int, default=115200, help='Baud rate (default: 115200)')

    args = parser.parse_args()

    if not Path(args.uf2_file).exists():
        print(f"Error: UF2 file '{args.uf2_file}' not found")
        return 1

    success = flash_uf2_file(args.port, args.uf2_file, args.baud)
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())