/* ----------------------------------------------------------------------------
 * UART UF2 Flashing Protocol Header
 * ----------------------------------------------------------------------------
 * Copyright (c) 2024
 *
 * Simple protocol for flashing UF2 blocks over UART
 * ----------------------------------------------------------------------------
 */

#ifndef _UART_UF2_H_
#define _UART_UF2_H_

#include <stdint.h>
#include <stdbool.h>
#include "uf2format.h"

// Protocol constants
#define UF2_UART_MAGIC_START    0x55464232  // "UF2\x32" - UF2 UART protocol marker
#define UF2_UART_MAGIC_END      0x454E4432  // "END\x32" - End marker
#define UF2_UART_BLOCK_SIZE     512         // Standard UF2 block size
#define UF2_UART_HEADER_SIZE    12          // Protocol header size
#define UF2_UART_FOOTER_SIZE    8           // Protocol footer size
#define UF2_UART_FRAME_SIZE     (UF2_UART_HEADER_SIZE + UF2_UART_BLOCK_SIZE + UF2_UART_FOOTER_SIZE)

// Protocol commands
#define UF2_UART_CMD_INIT       0x01        // Initialize flashing session
#define UF2_UART_CMD_BLOCK      0x02        // Send UF2 block
#define UF2_UART_CMD_FINISH     0x03        // Finish flashing session
#define UF2_UART_CMD_RESET      0x04        // Reset device after flashing

// Response codes
#define UF2_UART_RESP_OK        0x06        // ACK - Success
#define UF2_UART_RESP_ERROR     0x15        // NAK - Error
#define UF2_UART_RESP_READY     0x52        // 'R' - Ready for next block
#define UF2_UART_RESP_BUSY      0x42        // 'B' - Busy, retry later

// Error codes
#define UF2_UART_ERR_NONE       0x00        // No error
#define UF2_UART_ERR_CHECKSUM   0x01        // Checksum mismatch
#define UF2_UART_ERR_INVALID    0x02        // Invalid UF2 block
#define UF2_UART_ERR_ADDRESS    0x03        // Invalid target address
#define UF2_UART_ERR_SEQUENCE   0x04        // Block sequence error
#define UF2_UART_ERR_TIMEOUT    0x05        // Communication timeout

// Protocol timeouts (in milliseconds)
#define UF2_UART_TIMEOUT_BLOCK  5000        // Timeout for block reception
#define UF2_UART_TIMEOUT_ACK    1000        // Timeout for ACK/NAK
#define UF2_UART_RETRY_COUNT    3           // Number of retries

// Configuration options (can be overridden in board_config.h)
#ifndef UF2_UART_BUFFER_SIZE
#define UF2_UART_BUFFER_SIZE    1024        // Size of receive buffer
#endif

#ifndef UF2_UART_MAX_BLOCKS
#define UF2_UART_MAX_BLOCKS     2048        // Maximum number of blocks in a session
#endif

#ifndef UF2_UART_BAUD_RATE
#define UF2_UART_BAUD_RATE      115200      // Default baud rate for UF2 protocol
#endif

/**
 * UART UF2 Protocol Frame Structure:
 *
 * Header (12 bytes):
 * +--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
 * | MAGIC_START (4) | CMD(1) | SEQ(2) | LEN(2) | RESERVED(1) | CHECKSUM_HDR(2) |
 * +--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
 *
 * Payload (512 bytes):
 * +--------+--------+--------+--------+
 * |     UF2 Block Data (512 bytes)   |
 * +--------+--------+--------+--------+
 *
 * Footer (8 bytes):
 * +--------+--------+--------+--------+--------+--------+--------+--------+
 * | CHECKSUM_DATA(4) | MAGIC_END (4) |
 * +--------+--------+--------+--------+--------+--------+--------+--------+
 */

// Protocol frame header
typedef struct __attribute__((packed)) {
    uint32_t magic_start;       // UF2_UART_MAGIC_START
    uint8_t  command;           // Command type
    uint16_t sequence;          // Block sequence number
    uint16_t length;            // Payload length (should be 512 for UF2 blocks)
    uint8_t  reserved;          // Reserved for future use
    uint16_t header_checksum;   // CRC16 of header (excluding this field)
} UF2_UART_Header;

// Protocol frame footer
typedef struct __attribute__((packed)) {
    uint32_t data_checksum;     // CRC32 of payload data
    uint32_t magic_end;         // UF2_UART_MAGIC_END
} UF2_UART_Footer;

// Complete protocol frame
typedef struct __attribute__((packed)) {
    UF2_UART_Header header;
    uint8_t data[UF2_UART_BLOCK_SIZE];
    UF2_UART_Footer footer;
} UF2_UART_Frame;

// Response frame
typedef struct __attribute__((packed)) {
    uint8_t response;           // Response code
    uint8_t error_code;         // Error code (if response is NAK)
    uint16_t sequence;          // Sequence number being acknowledged
} UF2_UART_Response;

// UART UF2 session state
typedef struct {
    bool active;                // Session is active
    uint32_t total_blocks;      // Total number of blocks expected
    uint32_t blocks_received;   // Number of blocks received
    uint16_t last_sequence;     // Last sequence number received
    WriteState write_state;     // UF2 write state tracking
    uint32_t start_time;        // Session start time
} UF2_UART_Session;

// Function prototypes

/**
 * Initialize UART UF2 flashing subsystem
 */
void uart_uf2_init(void);

/**
 * Process incoming UART data for UF2 protocol
 * Should be called regularly from main loop when UART data is available
 * @return true if UF2 data was processed, false otherwise
 */
bool uart_uf2_process(void);

/**
 * Check if UART UF2 session is active
 * @return true if session is active
 */
bool uart_uf2_is_active(void);

/**
 * Send response to host
 * @param response Response code
 * @param error_code Error code (if response is NAK)
 * @param sequence Sequence number being acknowledged
 */
void uart_uf2_send_response(uint8_t response, uint8_t error_code, uint16_t sequence);

/**
 * Calculate CRC16 checksum
 * @param data Data buffer
 * @param length Data length
 * @return CRC16 checksum
 */
uint16_t uart_uf2_crc16(const uint8_t *data, uint16_t length);

/**
 * Calculate CRC32 checksum
 * @param data Data buffer
 * @param length Data length
 * @return CRC32 checksum
 */
uint32_t uart_uf2_crc32(const uint8_t *data, uint32_t length);

/**
 * Reset UART UF2 session
 */
void uart_uf2_reset_session(void);

/**
 * Handle timeout in UART UF2 session
 */
void uart_uf2_handle_timeout(void);

#endif // _UART_UF2_H_