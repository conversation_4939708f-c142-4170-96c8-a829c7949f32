/* ----------------------------------------------------------------------------
 * UART Binary Flashing Protocol Header
 * ----------------------------------------------------------------------------
 * Copyright (c) 2024
 *
 * Simple protocol for flashing binary firmware over UART
 * ----------------------------------------------------------------------------
 */

#ifndef _UART_FLASH_H_
#define _UART_FLASH_H_

#include <stdint.h>
#include <stdbool.h>

// Protocol constants
#define UART_FLASH_MAGIC_START  0x42494E46  // "BINF" - Binary Flash protocol marker
#define UART_FLASH_MAGIC_END    0x454E4446  // "ENDF" - End marker
#define UART_FLASH_BLOCK_SIZE   256         // Flash page size for most SAMD chips
#define UART_FLASH_HEADER_SIZE  16          // Protocol header size
#define UART_FLASH_FRAME_SIZE   (UART_FLASH_HEADER_SIZE + UART_FLASH_BLOCK_SIZE + 4)

// Protocol commands
#define UART_FLASH_CMD_START    0x01        // Start flashing session with total size
#define UART_FLASH_CMD_DATA     0x02        // Send data block
#define UART_FLASH_CMD_FINISH   0x03        // Finish flashing session
#define UART_FLASH_CMD_RESET    0x04        // Reset device after flashing

// Response codes
#define UART_FLASH_RESP_OK      0x06        // ACK - Success
#define UART_FLASH_RESP_ERROR   0x15        // NAK - Error
#define UART_FLASH_RESP_READY   0x52        // 'R' - Ready for next block

// Error codes
#define UART_FLASH_ERR_NONE     0x00        // No error
#define UART_FLASH_ERR_CHECKSUM 0x01        // Checksum mismatch
#define UART_FLASH_ERR_ADDRESS  0x02        // Invalid target address
#define UART_FLASH_ERR_SIZE     0x03        // Invalid size
#define UART_FLASH_ERR_SEQUENCE 0x04        // Block sequence error
#define UART_FLASH_ERR_TIMEOUT  0x05        // Communication timeout

// Protocol timeouts (in milliseconds)
#define UART_FLASH_TIMEOUT_BLOCK 5000       // Timeout for block reception
#define UART_FLASH_TIMEOUT_ACK   1000       // Timeout for ACK/NAK

// Configuration options (can be overridden in board_config.h)
#ifndef UART_FLASH_BUFFER_SIZE
#define UART_FLASH_BUFFER_SIZE   512        // Size of receive buffer
#endif

#ifndef UART_FLASH_MAX_SIZE
#define UART_FLASH_MAX_SIZE      (256*1024) // Maximum firmware size (256KB)
#endif

/**
 * UART Binary Flash Protocol Frame Structure:
 *
 * Header (16 bytes):
 * +--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
 * | MAGIC_START (4) | CMD(1) | SEQ(2) | ADDR(4) | LEN(2) | TOTAL_SIZE(4) | CHECKSUM(1) |
 * +--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
 *
 * Data (256 bytes):
 * +--------+--------+--------+--------+
 * |    Binary Data (256 bytes)      |
 * +--------+--------+--------+--------+
 *
 * Footer (4 bytes):
 * +--------+--------+--------+--------+
 * | MAGIC_END (4) |
 * +--------+--------+--------+--------+
 */

// Protocol frame header
typedef struct __attribute__((packed)) {
    uint32_t magic_start;       // UART_FLASH_MAGIC_START
    uint8_t  command;           // Command type
    uint16_t sequence;          // Block sequence number
    uint32_t address;           // Target flash address
    uint16_t length;            // Data length in this frame
    uint32_t total_size;        // Total firmware size (for START command)
    uint8_t  checksum;          // Simple XOR checksum of header
} UART_Flash_Header;

// Complete protocol frame
typedef struct __attribute__((packed)) {
    UART_Flash_Header header;
    uint8_t data[UART_FLASH_BLOCK_SIZE];
    uint32_t magic_end;         // UART_FLASH_MAGIC_END
} UART_Flash_Frame;

// Response frame
typedef struct __attribute__((packed)) {
    uint8_t response;           // Response code
    uint8_t error_code;         // Error code (if response is NAK)
    uint16_t sequence;          // Sequence number being acknowledged
} UART_Flash_Response;

// UART Flash session state
typedef struct {
    bool active;                // Session is active
    uint32_t total_size;        // Total firmware size expected
    uint32_t bytes_received;    // Number of bytes received
    uint32_t current_address;   // Current flash address
    uint16_t last_sequence;     // Last sequence number received
    uint32_t start_time;        // Session start time
} UART_Flash_Session;

// Function prototypes

/**
 * Initialize UART flash subsystem
 */
void uart_flash_init(void);

/**
 * Process incoming UART data for binary flash protocol
 * Should be called regularly from main loop when UART data is available
 * @return true if flash data was processed, false otherwise
 */
bool uart_flash_process(void);

/**
 * Check if UART flash session is active
 * @return true if session is active
 */
bool uart_flash_is_active(void);

/**
 * Send response to host
 * @param response Response code
 * @param error_code Error code (if response is NAK)
 * @param sequence Sequence number being acknowledged
 */
void uart_flash_send_response(uint8_t response, uint8_t error_code, uint16_t sequence);

/**
 * Calculate simple XOR checksum
 * @param data Data buffer
 * @param length Data length
 * @return XOR checksum
 */
uint8_t uart_flash_checksum(const uint8_t *data, uint16_t length);

/**
 * Reset UART flash session
 */
void uart_flash_reset_session(void);

/**
 * Handle timeout in UART flash session
 */
void uart_flash_handle_timeout(void);

#endif // _UART_FLASH_H_