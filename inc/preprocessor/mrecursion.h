/* ----------------------------------------------------------------------------
 *         SAM Software Package License
 * ----------------------------------------------------------------------------
 * Copyright (c) 2011-2014, Atmel Corporation
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following condition is met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the disclaimer below.
 *
 * <PERSON><PERSON>'s name may not be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * DISCLAIMER: THIS SOFTWARE IS PROVIDED BY ATMEL "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT ARE
 * DISCLAIMED. IN NO EVENT SHALL ATMEL BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 * ----------------------------------------------------------------------------
 */

#ifndef _MRECURSION_H_
#define _MRECURSION_H_

/**
 * \defgroup group_sam0_utils_mrecursion Preprocessor - Macro Recursion
 *
 * \ingroup group_sam0_utils
 *
 * @{
 */

#include "preprocessor.h"

#define DEC_256                                   255
#define DEC_255                                   254
#define DEC_254                                   253
#define DEC_253                                   252
#define DEC_252                                   251
#define DEC_251                                   250
#define DEC_250                                   249
#define DEC_249                                   248
#define DEC_248                                   247
#define DEC_247                                   246
#define DEC_246                                   245
#define DEC_245                                   244
#define DEC_244                                   243
#define DEC_243                                   242
#define DEC_242                                   241
#define DEC_241                                   240
#define DEC_240                                   239
#define DEC_239                                   238
#define DEC_238                                   237
#define DEC_237                                   236
#define DEC_236                                   235
#define DEC_235                                   234
#define DEC_234                                   233
#define DEC_233                                   232
#define DEC_232                                   231
#define DEC_231                                   230
#define DEC_230                                   229
#define DEC_229                                   228
#define DEC_228                                   227
#define DEC_227                                   226
#define DEC_226                                   225
#define DEC_225                                   224
#define DEC_224                                   223
#define DEC_223                                   222
#define DEC_222                                   221
#define DEC_221                                   220
#define DEC_220                                   219
#define DEC_219                                   218
#define DEC_218                                   217
#define DEC_217                                   216
#define DEC_216                                   215
#define DEC_215                                   214
#define DEC_214                                   213
#define DEC_213                                   212
#define DEC_212                                   211
#define DEC_211                                   210
#define DEC_210                                   209
#define DEC_209                                   208
#define DEC_208                                   207
#define DEC_207                                   206
#define DEC_206                                   205
#define DEC_205                                   204
#define DEC_204                                   203
#define DEC_203                                   202
#define DEC_202                                   201
#define DEC_201                                   200
#define DEC_200                                   199
#define DEC_199                                   198
#define DEC_198                                   197
#define DEC_197                                   196
#define DEC_196                                   195
#define DEC_195                                   194
#define DEC_194                                   193
#define DEC_193                                   192
#define DEC_192                                   191
#define DEC_191                                   190
#define DEC_190                                   189
#define DEC_189                                   188
#define DEC_188                                   187
#define DEC_187                                   186
#define DEC_186                                   185
#define DEC_185                                   184
#define DEC_184                                   183
#define DEC_183                                   182
#define DEC_182                                   181
#define DEC_181                                   180
#define DEC_180                                   179
#define DEC_179                                   178
#define DEC_178                                   177
#define DEC_177                                   176
#define DEC_176                                   175
#define DEC_175                                   174
#define DEC_174                                   173
#define DEC_173                                   172
#define DEC_172                                   171
#define DEC_171                                   170
#define DEC_170                                   169
#define DEC_169                                   168
#define DEC_168                                   167
#define DEC_167                                   166
#define DEC_166                                   165
#define DEC_165                                   164
#define DEC_164                                   163
#define DEC_163                                   162
#define DEC_162                                   161
#define DEC_161                                   160
#define DEC_160                                   159
#define DEC_159                                   158
#define DEC_158                                   157
#define DEC_157                                   156
#define DEC_156                                   155
#define DEC_155                                   154
#define DEC_154                                   153
#define DEC_153                                   152
#define DEC_152                                   151
#define DEC_151                                   150
#define DEC_150                                   149
#define DEC_149                                   148
#define DEC_148                                   147
#define DEC_147                                   146
#define DEC_146                                   145
#define DEC_145                                   144
#define DEC_144                                   143
#define DEC_143                                   142
#define DEC_142                                   141
#define DEC_141                                   140
#define DEC_140                                   139
#define DEC_139                                   138
#define DEC_138                                   137
#define DEC_137                                   136
#define DEC_136                                   135
#define DEC_135                                   134
#define DEC_134                                   133
#define DEC_133                                   132
#define DEC_132                                   131
#define DEC_131                                   130
#define DEC_130                                   129
#define DEC_129                                   128
#define DEC_128                                   127
#define DEC_127                                   126
#define DEC_126                                   125
#define DEC_125                                   124
#define DEC_124                                   123
#define DEC_123                                   122
#define DEC_122                                   121
#define DEC_121                                   120
#define DEC_120                                   119
#define DEC_119                                   118
#define DEC_118                                   117
#define DEC_117                                   116
#define DEC_116                                   115
#define DEC_115                                   114
#define DEC_114                                   113
#define DEC_113                                   112
#define DEC_112                                   111
#define DEC_111                                   110
#define DEC_110                                   109
#define DEC_109                                   108
#define DEC_108                                   107
#define DEC_107                                   106
#define DEC_106                                   105
#define DEC_105                                   104
#define DEC_104                                   103
#define DEC_103                                   102
#define DEC_102                                   101
#define DEC_101                                   100
#define DEC_100                                    99
#define DEC_99                                     98
#define DEC_98                                     97
#define DEC_97                                     96
#define DEC_96                                     95
#define DEC_95                                     94
#define DEC_94                                     93
#define DEC_93                                     92
#define DEC_92                                     91
#define DEC_91                                     90
#define DEC_90                                     89
#define DEC_89                                     88
#define DEC_88                                     87
#define DEC_87                                     86
#define DEC_86                                     85
#define DEC_85                                     84
#define DEC_84                                     83
#define DEC_83                                     82
#define DEC_82                                     81
#define DEC_81                                     80
#define DEC_80                                     79
#define DEC_79                                     78
#define DEC_78                                     77
#define DEC_77                                     76
#define DEC_76                                     75
#define DEC_75                                     74
#define DEC_74                                     73
#define DEC_73                                     72
#define DEC_72                                     71
#define DEC_71                                     70
#define DEC_70                                     69
#define DEC_69                                     68
#define DEC_68                                     67
#define DEC_67                                     66
#define DEC_66                                     65
#define DEC_65                                     64
#define DEC_64                                     63
#define DEC_63                                     62
#define DEC_62                                     61
#define DEC_61                                     60
#define DEC_60                                     59
#define DEC_59                                     58
#define DEC_58                                     57
#define DEC_57                                     56
#define DEC_56                                     55
#define DEC_55                                     54
#define DEC_54                                     53
#define DEC_53                                     52
#define DEC_52                                     51
#define DEC_51                                     50
#define DEC_50                                     49
#define DEC_49                                     48
#define DEC_48                                     47
#define DEC_47                                     46
#define DEC_46                                     45
#define DEC_45                                     44
#define DEC_44                                     43
#define DEC_43                                     42
#define DEC_42                                     41
#define DEC_41                                     40
#define DEC_40                                     39
#define DEC_39                                     38
#define DEC_38                                     37
#define DEC_37                                     36
#define DEC_36                                     35
#define DEC_35                                     34
#define DEC_34                                     33
#define DEC_33                                     32
#define DEC_32                                     31
#define DEC_31                                     30
#define DEC_30                                     29
#define DEC_29                                     28
#define DEC_28                                     27
#define DEC_27                                     26
#define DEC_26                                     25
#define DEC_25                                     24
#define DEC_24                                     23
#define DEC_23                                     22
#define DEC_22                                     21
#define DEC_21                                     20
#define DEC_20                                     19
#define DEC_19                                     18
#define DEC_18                                     17
#define DEC_17                                     16
#define DEC_16                                     15
#define DEC_15                                     14
#define DEC_14                                     13
#define DEC_13                                     12
#define DEC_12                                     11
#define DEC_11                                     10
#define DEC_10                                      9
#define DEC_9                                       8
#define DEC_8                                       7
#define DEC_7                                       6
#define DEC_6                                       5
#define DEC_5                                       4
#define DEC_4                                       3
#define DEC_3                                       2
#define DEC_2                                       1
#define DEC_1                                       0
#define DEC_(n)                                     DEC_##n


/** Maximal number of repetitions supported by MRECURSION. */
#define MRECURSION_LIMIT   256

/** \brief Macro recursion.
 *
 * This macro represents a horizontal repetition construct.
 *
 * \param[in] count  The number of repetitious calls to macro. Valid values
 *                   range from 0 to MRECURSION_LIMIT.
 * \param[in] macro  A binary operation of the form macro(data, n).  This macro
 *                   is expanded by MRECURSION with the current repetition number
 *                   and the auxiliary data argument.
 * \param[in] data   A recursive threshold, building on this to decline by times 
 *                   defined with param count.
 *
 * \return       <tt>macro(data-count+1,0) macro(data-count+2,1)...macro(data,count-1)</tt>
 */
#define MRECURSION(count, macro, data) TPASTE2(MRECURSION, count) (macro, data)

#define MRECURSION0(  macro, data)
#define MRECURSION1(  macro, data)    MRECURSION0(  macro, DEC_(data))   macro(data, 0)
#define MRECURSION2(  macro, data)    MRECURSION1(  macro, DEC_(data))   macro(data, 1)
#define MRECURSION3(  macro, data)    MRECURSION2(  macro, DEC_(data))   macro(data, 2)
#define MRECURSION4(  macro, data)    MRECURSION3(  macro, DEC_(data))   macro(data, 3)
#define MRECURSION5(  macro, data)    MRECURSION4(  macro, DEC_(data))   macro(data, 4)
#define MRECURSION6(  macro, data)    MRECURSION5(  macro, DEC_(data))   macro(data, 5)
#define MRECURSION7(  macro, data)    MRECURSION6(  macro, DEC_(data))   macro(data, 6)
#define MRECURSION8(  macro, data)    MRECURSION7(  macro, DEC_(data))   macro(data, 7)
#define MRECURSION9(  macro, data)    MRECURSION8(  macro, DEC_(data))   macro(data, 8)
#define MRECURSION10( macro, data)    MRECURSION9(  macro, DEC_(data))   macro(data, 9)
#define MRECURSION11( macro, data)    MRECURSION10(  macro, DEC_(data))   macro(data, 10)
#define MRECURSION12( macro, data)    MRECURSION11(  macro, DEC_(data))   macro(data, 11)
#define MRECURSION13( macro, data)    MRECURSION12(  macro, DEC_(data))   macro(data, 12)
#define MRECURSION14( macro, data)    MRECURSION13(  macro, DEC_(data))   macro(data, 13)
#define MRECURSION15( macro, data)    MRECURSION14(  macro, DEC_(data))   macro(data, 14)
#define MRECURSION16( macro, data)    MRECURSION15(  macro, DEC_(data))   macro(data, 15)
#define MRECURSION17( macro, data)    MRECURSION16(  macro, DEC_(data))   macro(data, 16)
#define MRECURSION18( macro, data)    MRECURSION17(  macro, DEC_(data))   macro(data, 17)
#define MRECURSION19( macro, data)    MRECURSION18(  macro, DEC_(data))   macro(data, 18)
#define MRECURSION20( macro, data)    MRECURSION19(  macro, DEC_(data))   macro(data, 19)
#define MRECURSION21( macro, data)    MRECURSION20(  macro, DEC_(data))   macro(data, 20)
#define MRECURSION22( macro, data)    MRECURSION21(  macro, DEC_(data))   macro(data, 21)
#define MRECURSION23( macro, data)    MRECURSION22(  macro, DEC_(data))   macro(data, 22)
#define MRECURSION24( macro, data)    MRECURSION23(  macro, DEC_(data))   macro(data, 23)
#define MRECURSION25( macro, data)    MRECURSION24(  macro, DEC_(data))   macro(data, 24)
#define MRECURSION26( macro, data)    MRECURSION25(  macro, DEC_(data))   macro(data, 25)
#define MRECURSION27( macro, data)    MRECURSION26(  macro, DEC_(data))   macro(data, 26)
#define MRECURSION28( macro, data)    MRECURSION27(  macro, DEC_(data))   macro(data, 27)
#define MRECURSION29( macro, data)    MRECURSION28(  macro, DEC_(data))   macro(data, 28)
#define MRECURSION30( macro, data)    MRECURSION29(  macro, DEC_(data))   macro(data, 29)
#define MRECURSION31( macro, data)    MRECURSION30(  macro, DEC_(data))   macro(data, 30)
#define MRECURSION32( macro, data)    MRECURSION31(  macro, DEC_(data))   macro(data, 31)
#define MRECURSION33( macro, data)    MRECURSION32(  macro, DEC_(data))   macro(data, 32)
#define MRECURSION34( macro, data)    MRECURSION33(  macro, DEC_(data))   macro(data, 33)
#define MRECURSION35( macro, data)    MRECURSION34(  macro, DEC_(data))   macro(data, 34)
#define MRECURSION36( macro, data)    MRECURSION35(  macro, DEC_(data))   macro(data, 35)
#define MRECURSION37( macro, data)    MRECURSION36(  macro, DEC_(data))   macro(data, 36)
#define MRECURSION38( macro, data)    MRECURSION37(  macro, DEC_(data))   macro(data, 37)
#define MRECURSION39( macro, data)    MRECURSION38(  macro, DEC_(data))   macro(data, 38)
#define MRECURSION40( macro, data)    MRECURSION39(  macro, DEC_(data))   macro(data, 39)
#define MRECURSION41( macro, data)    MRECURSION40(  macro, DEC_(data))   macro(data, 40)
#define MRECURSION42( macro, data)    MRECURSION41(  macro, DEC_(data))   macro(data, 41)
#define MRECURSION43( macro, data)    MRECURSION42(  macro, DEC_(data))   macro(data, 42)
#define MRECURSION44( macro, data)    MRECURSION43(  macro, DEC_(data))   macro(data, 43)
#define MRECURSION45( macro, data)    MRECURSION44(  macro, DEC_(data))   macro(data, 44)
#define MRECURSION46( macro, data)    MRECURSION45(  macro, DEC_(data))   macro(data, 45)
#define MRECURSION47( macro, data)    MRECURSION46(  macro, DEC_(data))   macro(data, 46)
#define MRECURSION48( macro, data)    MRECURSION47(  macro, DEC_(data))   macro(data, 47)
#define MRECURSION49( macro, data)    MRECURSION48(  macro, DEC_(data))   macro(data, 48)
#define MRECURSION50( macro, data)    MRECURSION49(  macro, DEC_(data))   macro(data, 49)
#define MRECURSION51( macro, data)    MRECURSION50(  macro, DEC_(data))   macro(data, 50)
#define MRECURSION52( macro, data)    MRECURSION51(  macro, DEC_(data))   macro(data, 51)
#define MRECURSION53( macro, data)    MRECURSION52(  macro, DEC_(data))   macro(data, 52)
#define MRECURSION54( macro, data)    MRECURSION53(  macro, DEC_(data))   macro(data, 53)
#define MRECURSION55( macro, data)    MRECURSION54(  macro, DEC_(data))   macro(data, 54)
#define MRECURSION56( macro, data)    MRECURSION55(  macro, DEC_(data))   macro(data, 55)
#define MRECURSION57( macro, data)    MRECURSION56(  macro, DEC_(data))   macro(data, 56)
#define MRECURSION58( macro, data)    MRECURSION57(  macro, DEC_(data))   macro(data, 57)
#define MRECURSION59( macro, data)    MRECURSION58(  macro, DEC_(data))   macro(data, 58)
#define MRECURSION60( macro, data)    MRECURSION59(  macro, DEC_(data))   macro(data, 59)
#define MRECURSION61( macro, data)    MRECURSION60(  macro, DEC_(data))   macro(data, 60)
#define MRECURSION62( macro, data)    MRECURSION61(  macro, DEC_(data))   macro(data, 61)
#define MRECURSION63( macro, data)    MRECURSION62(  macro, DEC_(data))   macro(data, 62)
#define MRECURSION64( macro, data)    MRECURSION63(  macro, DEC_(data))   macro(data, 63)
#define MRECURSION65( macro, data)    MRECURSION64(  macro, DEC_(data))   macro(data, 64)
#define MRECURSION66( macro, data)    MRECURSION65(  macro, DEC_(data))   macro(data, 65)
#define MRECURSION67( macro, data)    MRECURSION66(  macro, DEC_(data))   macro(data, 66)
#define MRECURSION68( macro, data)    MRECURSION67(  macro, DEC_(data))   macro(data, 67)
#define MRECURSION69( macro, data)    MRECURSION68(  macro, DEC_(data))   macro(data, 68)
#define MRECURSION70( macro, data)    MRECURSION69(  macro, DEC_(data))   macro(data, 69)
#define MRECURSION71( macro, data)    MRECURSION70(  macro, DEC_(data))   macro(data, 70)
#define MRECURSION72( macro, data)    MRECURSION71(  macro, DEC_(data))   macro(data, 71)
#define MRECURSION73( macro, data)    MRECURSION72(  macro, DEC_(data))   macro(data, 72)
#define MRECURSION74( macro, data)    MRECURSION73(  macro, DEC_(data))   macro(data, 73)
#define MRECURSION75( macro, data)    MRECURSION74(  macro, DEC_(data))   macro(data, 74)
#define MRECURSION76( macro, data)    MRECURSION75(  macro, DEC_(data))   macro(data, 75)
#define MRECURSION77( macro, data)    MRECURSION76(  macro, DEC_(data))   macro(data, 76)
#define MRECURSION78( macro, data)    MRECURSION77(  macro, DEC_(data))   macro(data, 77)
#define MRECURSION79( macro, data)    MRECURSION78(  macro, DEC_(data))   macro(data, 78)
#define MRECURSION80( macro, data)    MRECURSION79(  macro, DEC_(data))   macro(data, 79)
#define MRECURSION81( macro, data)    MRECURSION80(  macro, DEC_(data))   macro(data, 80)
#define MRECURSION82( macro, data)    MRECURSION81(  macro, DEC_(data))   macro(data, 81)
#define MRECURSION83( macro, data)    MRECURSION82(  macro, DEC_(data))   macro(data, 82)
#define MRECURSION84( macro, data)    MRECURSION83(  macro, DEC_(data))   macro(data, 83)
#define MRECURSION85( macro, data)    MRECURSION84(  macro, DEC_(data))   macro(data, 84)
#define MRECURSION86( macro, data)    MRECURSION85(  macro, DEC_(data))   macro(data, 85)
#define MRECURSION87( macro, data)    MRECURSION86(  macro, DEC_(data))   macro(data, 86)
#define MRECURSION88( macro, data)    MRECURSION87(  macro, DEC_(data))   macro(data, 87)
#define MRECURSION89( macro, data)    MRECURSION88(  macro, DEC_(data))   macro(data, 88)
#define MRECURSION90( macro, data)    MRECURSION89(  macro, DEC_(data))   macro(data, 89)
#define MRECURSION91( macro, data)    MRECURSION90(  macro, DEC_(data))   macro(data, 90)
#define MRECURSION92( macro, data)    MRECURSION91(  macro, DEC_(data))   macro(data, 91)
#define MRECURSION93( macro, data)    MRECURSION92(  macro, DEC_(data))   macro(data, 92)
#define MRECURSION94( macro, data)    MRECURSION93(  macro, DEC_(data))   macro(data, 93)
#define MRECURSION95( macro, data)    MRECURSION94(  macro, DEC_(data))   macro(data, 94)
#define MRECURSION96( macro, data)    MRECURSION95(  macro, DEC_(data))   macro(data, 95)
#define MRECURSION97( macro, data)    MRECURSION96(  macro, DEC_(data))   macro(data, 96)
#define MRECURSION98( macro, data)    MRECURSION97(  macro, DEC_(data))   macro(data, 97)
#define MRECURSION99( macro, data)    MRECURSION98(  macro, DEC_(data))   macro(data, 98)
#define MRECURSION100(macro, data)    MRECURSION99(  macro, DEC_(data))   macro(data, 99)
#define MRECURSION101(macro, data)    MRECURSION100(  macro, DEC_(data))   macro(data, 100)
#define MRECURSION102(macro, data)    MRECURSION101(  macro, DEC_(data))   macro(data, 101)
#define MRECURSION103(macro, data)    MRECURSION102(  macro, DEC_(data))   macro(data, 102)
#define MRECURSION104(macro, data)    MRECURSION103(  macro, DEC_(data))   macro(data, 103)
#define MRECURSION105(macro, data)    MRECURSION104(  macro, DEC_(data))   macro(data, 104)
#define MRECURSION106(macro, data)    MRECURSION105(  macro, DEC_(data))   macro(data, 105)
#define MRECURSION107(macro, data)    MRECURSION106(  macro, DEC_(data))   macro(data, 106)
#define MRECURSION108(macro, data)    MRECURSION107(  macro, DEC_(data))   macro(data, 107)
#define MRECURSION109(macro, data)    MRECURSION108(  macro, DEC_(data))   macro(data, 108)
#define MRECURSION110(macro, data)    MRECURSION109(  macro, DEC_(data))   macro(data, 109)
#define MRECURSION111(macro, data)    MRECURSION110(  macro, DEC_(data))   macro(data, 110)
#define MRECURSION112(macro, data)    MRECURSION111(  macro, DEC_(data))   macro(data, 111)
#define MRECURSION113(macro, data)    MRECURSION112(  macro, DEC_(data))   macro(data, 112)
#define MRECURSION114(macro, data)    MRECURSION113(  macro, DEC_(data))   macro(data, 113)
#define MRECURSION115(macro, data)    MRECURSION114(  macro, DEC_(data))   macro(data, 114)
#define MRECURSION116(macro, data)    MRECURSION115(  macro, DEC_(data))   macro(data, 115)
#define MRECURSION117(macro, data)    MRECURSION116(  macro, DEC_(data))   macro(data, 116)
#define MRECURSION118(macro, data)    MRECURSION117(  macro, DEC_(data))   macro(data, 117)
#define MRECURSION119(macro, data)    MRECURSION118(  macro, DEC_(data))   macro(data, 118)
#define MRECURSION120(macro, data)    MRECURSION119(  macro, DEC_(data))   macro(data, 119)
#define MRECURSION121(macro, data)    MRECURSION120(  macro, DEC_(data))   macro(data, 120)
#define MRECURSION122(macro, data)    MRECURSION121(  macro, DEC_(data))   macro(data, 121)
#define MRECURSION123(macro, data)    MRECURSION122(  macro, DEC_(data))   macro(data, 122)
#define MRECURSION124(macro, data)    MRECURSION123(  macro, DEC_(data))   macro(data, 123)
#define MRECURSION125(macro, data)    MRECURSION124(  macro, DEC_(data))   macro(data, 124)
#define MRECURSION126(macro, data)    MRECURSION125(  macro, DEC_(data))   macro(data, 125)
#define MRECURSION127(macro, data)    MRECURSION126(  macro, DEC_(data))   macro(data, 126)
#define MRECURSION128(macro, data)    MRECURSION127(  macro, DEC_(data))   macro(data, 127)
#define MRECURSION129(macro, data)    MRECURSION128(  macro, DEC_(data))   macro(data, 128)
#define MRECURSION130(macro, data)    MRECURSION129(  macro, DEC_(data))   macro(data, 129)
#define MRECURSION131(macro, data)    MRECURSION130(  macro, DEC_(data))   macro(data, 130)
#define MRECURSION132(macro, data)    MRECURSION131(  macro, DEC_(data))   macro(data, 131)
#define MRECURSION133(macro, data)    MRECURSION132(  macro, DEC_(data))   macro(data, 132)
#define MRECURSION134(macro, data)    MRECURSION133(  macro, DEC_(data))   macro(data, 133)
#define MRECURSION135(macro, data)    MRECURSION134(  macro, DEC_(data))   macro(data, 134)
#define MRECURSION136(macro, data)    MRECURSION135(  macro, DEC_(data))   macro(data, 135)
#define MRECURSION137(macro, data)    MRECURSION136(  macro, DEC_(data))   macro(data, 136)
#define MRECURSION138(macro, data)    MRECURSION137(  macro, DEC_(data))   macro(data, 137)
#define MRECURSION139(macro, data)    MRECURSION138(  macro, DEC_(data))   macro(data, 138)
#define MRECURSION140(macro, data)    MRECURSION139(  macro, DEC_(data))   macro(data, 139)
#define MRECURSION141(macro, data)    MRECURSION140(  macro, DEC_(data))   macro(data, 140)
#define MRECURSION142(macro, data)    MRECURSION141(  macro, DEC_(data))   macro(data, 141)
#define MRECURSION143(macro, data)    MRECURSION142(  macro, DEC_(data))   macro(data, 142)
#define MRECURSION144(macro, data)    MRECURSION143(  macro, DEC_(data))   macro(data, 143)
#define MRECURSION145(macro, data)    MRECURSION144(  macro, DEC_(data))   macro(data, 144)
#define MRECURSION146(macro, data)    MRECURSION145(  macro, DEC_(data))   macro(data, 145)
#define MRECURSION147(macro, data)    MRECURSION146(  macro, DEC_(data))   macro(data, 146)
#define MRECURSION148(macro, data)    MRECURSION147(  macro, DEC_(data))   macro(data, 147)
#define MRECURSION149(macro, data)    MRECURSION148(  macro, DEC_(data))   macro(data, 148)
#define MRECURSION150(macro, data)    MRECURSION149(  macro, DEC_(data))   macro(data, 149)
#define MRECURSION151(macro, data)    MRECURSION150(  macro, DEC_(data))   macro(data, 150)
#define MRECURSION152(macro, data)    MRECURSION151(  macro, DEC_(data))   macro(data, 151)
#define MRECURSION153(macro, data)    MRECURSION152(  macro, DEC_(data))   macro(data, 152)
#define MRECURSION154(macro, data)    MRECURSION153(  macro, DEC_(data))   macro(data, 153)
#define MRECURSION155(macro, data)    MRECURSION154(  macro, DEC_(data))   macro(data, 154)
#define MRECURSION156(macro, data)    MRECURSION155(  macro, DEC_(data))   macro(data, 155)
#define MRECURSION157(macro, data)    MRECURSION156(  macro, DEC_(data))   macro(data, 156)
#define MRECURSION158(macro, data)    MRECURSION157(  macro, DEC_(data))   macro(data, 157)
#define MRECURSION159(macro, data)    MRECURSION158(  macro, DEC_(data))   macro(data, 158)
#define MRECURSION160(macro, data)    MRECURSION159(  macro, DEC_(data))   macro(data, 159)
#define MRECURSION161(macro, data)    MRECURSION160(  macro, DEC_(data))   macro(data, 160)
#define MRECURSION162(macro, data)    MRECURSION161(  macro, DEC_(data))   macro(data, 161)
#define MRECURSION163(macro, data)    MRECURSION162(  macro, DEC_(data))   macro(data, 162)
#define MRECURSION164(macro, data)    MRECURSION163(  macro, DEC_(data))   macro(data, 163)
#define MRECURSION165(macro, data)    MRECURSION164(  macro, DEC_(data))   macro(data, 164)
#define MRECURSION166(macro, data)    MRECURSION165(  macro, DEC_(data))   macro(data, 165)
#define MRECURSION167(macro, data)    MRECURSION166(  macro, DEC_(data))   macro(data, 166)
#define MRECURSION168(macro, data)    MRECURSION167(  macro, DEC_(data))   macro(data, 167)
#define MRECURSION169(macro, data)    MRECURSION168(  macro, DEC_(data))   macro(data, 168)
#define MRECURSION170(macro, data)    MRECURSION169(  macro, DEC_(data))   macro(data, 169)
#define MRECURSION171(macro, data)    MRECURSION170(  macro, DEC_(data))   macro(data, 170)
#define MRECURSION172(macro, data)    MRECURSION171(  macro, DEC_(data))   macro(data, 171)
#define MRECURSION173(macro, data)    MRECURSION172(  macro, DEC_(data))   macro(data, 172)
#define MRECURSION174(macro, data)    MRECURSION173(  macro, DEC_(data))   macro(data, 173)
#define MRECURSION175(macro, data)    MRECURSION174(  macro, DEC_(data))   macro(data, 174)
#define MRECURSION176(macro, data)    MRECURSION175(  macro, DEC_(data))   macro(data, 175)
#define MRECURSION177(macro, data)    MRECURSION176(  macro, DEC_(data))   macro(data, 176)
#define MRECURSION178(macro, data)    MRECURSION177(  macro, DEC_(data))   macro(data, 177)
#define MRECURSION179(macro, data)    MRECURSION178(  macro, DEC_(data))   macro(data, 178)
#define MRECURSION180(macro, data)    MRECURSION179(  macro, DEC_(data))   macro(data, 179)
#define MRECURSION181(macro, data)    MRECURSION180(  macro, DEC_(data))   macro(data, 180)
#define MRECURSION182(macro, data)    MRECURSION181(  macro, DEC_(data))   macro(data, 181)
#define MRECURSION183(macro, data)    MRECURSION182(  macro, DEC_(data))   macro(data, 182)
#define MRECURSION184(macro, data)    MRECURSION183(  macro, DEC_(data))   macro(data, 183)
#define MRECURSION185(macro, data)    MRECURSION184(  macro, DEC_(data))   macro(data, 184)
#define MRECURSION186(macro, data)    MRECURSION185(  macro, DEC_(data))   macro(data, 185)
#define MRECURSION187(macro, data)    MRECURSION186(  macro, DEC_(data))   macro(data, 186)
#define MRECURSION188(macro, data)    MRECURSION187(  macro, DEC_(data))   macro(data, 187)
#define MRECURSION189(macro, data)    MRECURSION188(  macro, DEC_(data))   macro(data, 188)
#define MRECURSION190(macro, data)    MRECURSION189(  macro, DEC_(data))   macro(data, 189)
#define MRECURSION191(macro, data)    MRECURSION190(  macro, DEC_(data))   macro(data, 190)
#define MRECURSION192(macro, data)    MRECURSION191(  macro, DEC_(data))   macro(data, 191)
#define MRECURSION193(macro, data)    MRECURSION192(  macro, DEC_(data))   macro(data, 192)
#define MRECURSION194(macro, data)    MRECURSION193(  macro, DEC_(data))   macro(data, 193)
#define MRECURSION195(macro, data)    MRECURSION194(  macro, DEC_(data))   macro(data, 194)
#define MRECURSION196(macro, data)    MRECURSION195(  macro, DEC_(data))   macro(data, 195)
#define MRECURSION197(macro, data)    MRECURSION196(  macro, DEC_(data))   macro(data, 196)
#define MRECURSION198(macro, data)    MRECURSION197(  macro, DEC_(data))   macro(data, 197)
#define MRECURSION199(macro, data)    MRECURSION198(  macro, DEC_(data))   macro(data, 198)
#define MRECURSION200(macro, data)    MRECURSION199(  macro, DEC_(data))   macro(data, 199)
#define MRECURSION201(macro, data)    MRECURSION200(  macro, DEC_(data))   macro(data, 200)
#define MRECURSION202(macro, data)    MRECURSION201(  macro, DEC_(data))   macro(data, 201)
#define MRECURSION203(macro, data)    MRECURSION202(  macro, DEC_(data))   macro(data, 202)
#define MRECURSION204(macro, data)    MRECURSION203(  macro, DEC_(data))   macro(data, 203)
#define MRECURSION205(macro, data)    MRECURSION204(  macro, DEC_(data))   macro(data, 204)
#define MRECURSION206(macro, data)    MRECURSION205(  macro, DEC_(data))   macro(data, 205)
#define MRECURSION207(macro, data)    MRECURSION206(  macro, DEC_(data))   macro(data, 206)
#define MRECURSION208(macro, data)    MRECURSION207(  macro, DEC_(data))   macro(data, 207)
#define MRECURSION209(macro, data)    MRECURSION208(  macro, DEC_(data))   macro(data, 208)
#define MRECURSION210(macro, data)    MRECURSION209(  macro, DEC_(data))   macro(data, 209)
#define MRECURSION211(macro, data)    MRECURSION210(  macro, DEC_(data))   macro(data, 210)
#define MRECURSION212(macro, data)    MRECURSION211(  macro, DEC_(data))   macro(data, 211)
#define MRECURSION213(macro, data)    MRECURSION212(  macro, DEC_(data))   macro(data, 212)
#define MRECURSION214(macro, data)    MRECURSION213(  macro, DEC_(data))   macro(data, 213)
#define MRECURSION215(macro, data)    MRECURSION214(  macro, DEC_(data))   macro(data, 214)
#define MRECURSION216(macro, data)    MRECURSION215(  macro, DEC_(data))   macro(data, 215)
#define MRECURSION217(macro, data)    MRECURSION216(  macro, DEC_(data))   macro(data, 216)
#define MRECURSION218(macro, data)    MRECURSION217(  macro, DEC_(data))   macro(data, 217)
#define MRECURSION219(macro, data)    MRECURSION218(  macro, DEC_(data))   macro(data, 218)
#define MRECURSION220(macro, data)    MRECURSION219(  macro, DEC_(data))   macro(data, 219)
#define MRECURSION221(macro, data)    MRECURSION220(  macro, DEC_(data))   macro(data, 220)
#define MRECURSION222(macro, data)    MRECURSION221(  macro, DEC_(data))   macro(data, 221)
#define MRECURSION223(macro, data)    MRECURSION222(  macro, DEC_(data))   macro(data, 222)
#define MRECURSION224(macro, data)    MRECURSION223(  macro, DEC_(data))   macro(data, 223)
#define MRECURSION225(macro, data)    MRECURSION224(  macro, DEC_(data))   macro(data, 224)
#define MRECURSION226(macro, data)    MRECURSION225(  macro, DEC_(data))   macro(data, 225)
#define MRECURSION227(macro, data)    MRECURSION226(  macro, DEC_(data))   macro(data, 226)
#define MRECURSION228(macro, data)    MRECURSION227(  macro, DEC_(data))   macro(data, 227)
#define MRECURSION229(macro, data)    MRECURSION228(  macro, DEC_(data))   macro(data, 228)
#define MRECURSION230(macro, data)    MRECURSION229(  macro, DEC_(data))   macro(data, 229)
#define MRECURSION231(macro, data)    MRECURSION230(  macro, DEC_(data))   macro(data, 230)
#define MRECURSION232(macro, data)    MRECURSION231(  macro, DEC_(data))   macro(data, 231)
#define MRECURSION233(macro, data)    MRECURSION232(  macro, DEC_(data))   macro(data, 232)
#define MRECURSION234(macro, data)    MRECURSION233(  macro, DEC_(data))   macro(data, 233)
#define MRECURSION235(macro, data)    MRECURSION234(  macro, DEC_(data))   macro(data, 234)
#define MRECURSION236(macro, data)    MRECURSION235(  macro, DEC_(data))   macro(data, 235)
#define MRECURSION237(macro, data)    MRECURSION236(  macro, DEC_(data))   macro(data, 236)
#define MRECURSION238(macro, data)    MRECURSION237(  macro, DEC_(data))   macro(data, 237)
#define MRECURSION239(macro, data)    MRECURSION238(  macro, DEC_(data))   macro(data, 238)
#define MRECURSION240(macro, data)    MRECURSION239(  macro, DEC_(data))   macro(data, 239)
#define MRECURSION241(macro, data)    MRECURSION240(  macro, DEC_(data))   macro(data, 240)
#define MRECURSION242(macro, data)    MRECURSION241(  macro, DEC_(data))   macro(data, 241)
#define MRECURSION243(macro, data)    MRECURSION242(  macro, DEC_(data))   macro(data, 242)
#define MRECURSION244(macro, data)    MRECURSION243(  macro, DEC_(data))   macro(data, 243)
#define MRECURSION245(macro, data)    MRECURSION244(  macro, DEC_(data))   macro(data, 244)
#define MRECURSION246(macro, data)    MRECURSION245(  macro, DEC_(data))   macro(data, 245)
#define MRECURSION247(macro, data)    MRECURSION246(  macro, DEC_(data))   macro(data, 246)
#define MRECURSION248(macro, data)    MRECURSION247(  macro, DEC_(data))   macro(data, 247)
#define MRECURSION249(macro, data)    MRECURSION248(  macro, DEC_(data))   macro(data, 248)
#define MRECURSION250(macro, data)    MRECURSION249(  macro, DEC_(data))   macro(data, 249)
#define MRECURSION251(macro, data)    MRECURSION250(  macro, DEC_(data))   macro(data, 250)
#define MRECURSION252(macro, data)    MRECURSION251(  macro, DEC_(data))   macro(data, 251)
#define MRECURSION253(macro, data)    MRECURSION252(  macro, DEC_(data))   macro(data, 252)
#define MRECURSION254(macro, data)    MRECURSION253(  macro, DEC_(data))   macro(data, 253)
#define MRECURSION255(macro, data)    MRECURSION254(  macro, DEC_(data))   macro(data, 254)
#define MRECURSION256(macro, data)    MRECURSION255(  macro, DEC_(data))   macro(data, 255)

/** @} */

#endif  /* _MRECURSION_H_ */
