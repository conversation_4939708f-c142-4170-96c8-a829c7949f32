# UART UF2 Flashing Support

This document describes the UART UF2 flashing feature added to the UF2 bootloader for SAMD microcontrollers.

## Overview

The UART UF2 flashing feature allows firmware updates to be performed over a serial UART connection instead of USB. This is particularly useful for:

- Devices without USB connectivity
- Remote firmware updates over serial links
- Automated testing and deployment scenarios
- Recovery when USB is not functional

## Protocol Description

The UART UF2 protocol uses a simple framed format to transmit UF2 blocks over UART:

### Frame Structure

```
Header (12 bytes):
+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+
| MAGIC_START (4) | CMD(1) | SEQ(2) | LEN(2) | RESERVED(1) | CHECKSUM_HDR(2) |
+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+

Payload (512 bytes):
+--------+--------+--------+--------+
|     UF2 Block Data (512 bytes)   |
+--------+--------+--------+--------+

Footer (8 bytes):
+--------+--------+--------+--------+--------+--------+--------+--------+
| CHECKSUM_DATA(4) | MAGIC_END (4) |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### Commands

- `UF2_UART_CMD_INIT (0x01)`: Initialize flashing session
- `UF2_UART_CMD_BLOCK (0x02)`: Send UF2 block
- `UF2_UART_CMD_FINISH (0x03)`: Finish flashing session
- `UF2_UART_CMD_RESET (0x04)`: Reset device after flashing

### Response Codes

- `UF2_UART_RESP_OK (0x06)`: ACK - Success
- `UF2_UART_RESP_ERROR (0x15)`: NAK - Error
- `UF2_UART_RESP_READY (0x52)`: Ready for next block
- `UF2_UART_RESP_BUSY (0x42)`: Busy, retry later

## Configuration

### Enabling UART UF2 Support

To enable UART UF2 flashing, set the following in your board configuration or `inc/uf2.h`:

```c
#define USE_UART 1
#define USE_UART_UF2 1
```

### Configuration Options

The following options can be configured in `board_config.h`:

```c
// Size of receive buffer (default: 1024 bytes)
#define UF2_UART_BUFFER_SIZE    1024

// Maximum number of blocks in a session (default: 2048)
#define UF2_UART_MAX_BLOCKS     2048

// Default baud rate for UF2 protocol (default: 115200)
#define UF2_UART_BAUD_RATE      115200
```

## Usage

### Bootloader Behavior

1. The bootloader initializes UART and UART UF2 subsystem on startup
2. In the main loop, it checks for incoming UART data
3. If UF2 protocol data is detected, it enters UF2 flashing mode
4. If a '#' character is received, it falls back to SAM-BA monitor mode
5. LED color changes to indicate UART activity (COLOR_UART)

### Flashing Sequence

1. **Initialize**: Send INIT command with first UF2 block
2. **Transfer**: Send BLOCK commands with each UF2 block
3. **Finish**: Send FINISH command to complete flashing
4. **Reset**: Device automatically resets into application

### Error Handling

- Checksums are validated for both header and data
- Sequence numbers prevent duplicate/missing blocks
- Timeouts handle communication failures
- Retransmission is supported for reliability

## Implementation Details

### Files Added

- `inc/uart_uf2.h`: Protocol definitions and function prototypes
- `src/uart_uf2.c`: Implementation of UART UF2 protocol
- `UART_UF2_README.md`: This documentation

### Files Modified

- `inc/uf2.h`: Added configuration options and version string
- `src/main.c`: Integrated UART UF2 processing into main loop

### Memory Usage

The UART UF2 implementation adds approximately:
- 2KB of flash memory for code
- 1KB of RAM for buffers and state
- Configurable receive buffer size

## Testing

### Basic Functionality Test

1. Build bootloader with UART UF2 enabled
2. Connect UART to development board
3. Send UF2 file using protocol-compliant client
4. Verify firmware is flashed and device resets

### Compatibility Test

1. Verify SAM-BA monitor still works (send '#' character)
2. Verify USB MSC/HID flashing still works
3. Test with various UF2 file sizes
4. Test error conditions and recovery

## Limitations

- Requires UART hardware support on target board
- Slower than USB-based flashing
- No built-in flow control (relies on protocol-level ACK/NAK)
- Single-threaded implementation (blocks other operations during flashing)

## Future Enhancements

- Flow control support (RTS/CTS)
- Compression support for faster transfers
- Multi-block transfers in single frame
- Progress reporting
- Resume capability for interrupted transfers