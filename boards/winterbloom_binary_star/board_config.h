#ifndef BOARD_CONFIG_H
#define BOARD_CONFIG_H

#define VENDOR_NAME "Winterbloom"
#define PRODUCT_NAME "Binary Star"
#define VOLUME_LABEL "STARBOOT"
#define INDEX_URL "http://binarystar.wntr.dev"
#define BOARD_ID "SAMD51J20A-BinaryStar-v0"

// Allocated at https://github.com/adafruit/circuitpython/issues/4104
#define USB_VID 0x239A
#define USB_PID 0x00F5

#define LED_PIN PIN_PA23

#define BOARD_RGBLED_CLOCK_PIN PIN_PB09
#define BOARD_RGBLED_DATA_PIN PIN_PA04

#define BOOT_USART_MODULE SERCOM3
#define BOOT_USART_MASK APBAMASK
#define BOOT_USART_BUS_CLOCK_INDEX MCLK_APBBMASK_SERCOM3
#define BOOT_USART_PAD_SETTINGS UART_RX_PAD1_TX_PAD0
#define BOOT_USART_PAD3 PINMUX_UNUSED
#define BOOT_USART_PAD2 PINMUX_UNUSED
#define BOOT_USART_PAD1 PINMUX_PA22C_SERCOM3_PAD0
#define BOOT_USART_PAD0 PINMUX_PA23C_SERCOM3_PAD1
#define BOOT_GCLK_ID_CORE SERCOM3_GCLK_ID_CORE
#define BOOT_GCLK_ID_SLOW SERCOM3_GCLK_ID_SLOW

#endif
