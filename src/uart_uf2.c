/* ----------------------------------------------------------------------------
 * UART Binary Flashing Protocol Implementation
 * ----------------------------------------------------------------------------
 * Copyright (c) 2024
 *
 * Implementation of binary firmware flashing over UART
 * ----------------------------------------------------------------------------
 */

#include "uf2.h"

#if USE_UART && USE_UART_UF2

#include "uart_uf2.h"
#include "uart_driver.h"
#include "usart_sam_ba.h"
#include <string.h>

// Global state
static UART_Flash_Session g_flash_session = {0};
static uint8_t g_rx_buffer[UART_FLASH_FRAME_SIZE];
static uint32_t g_rx_buffer_pos = 0;
static uint32_t g_last_activity_time = 0;

// Forward declarations
static bool uart_flash_receive_frame(UART_Flash_Frame *frame);
static bool uart_flash_validate_frame(const UART_Flash_Frame *frame);
static void uart_flash_process_frame(const UART_Flash_Frame *frame);
static void uart_flash_handle_start_command(const UART_Flash_Frame *frame);
static void uart_flash_handle_data_command(const UART_Flash_Frame *frame);
static void uart_flash_handle_finish_command(const UART_Flash_Frame *frame);
static void uart_flash_handle_reset_command(const UART_Flash_Frame *frame);

/**
 * Initialize UART flash subsystem
 */
void uart_flash_init(void) {
    memset(&g_flash_session, 0, sizeof(g_flash_session));
    g_rx_buffer_pos = 0;
    g_last_activity_time = timerHigh;
}

/**
 * Process incoming UART data for binary flash protocol
 * Should be called regularly from main loop when UART data is available
 * @return true if flash data was processed, false otherwise
 */
bool uart_flash_process(void) {
    // Check for timeout
    if (g_flash_session.active && (timerHigh - g_last_activity_time) > UART_FLASH_TIMEOUT_BLOCK) {
        uart_flash_handle_timeout();
        return false;
    }

    // Try to receive a complete frame
    UART_Flash_Frame frame;
    if (uart_flash_receive_frame(&frame)) {
        g_last_activity_time = timerHigh;

        // Validate the frame
        if (uart_flash_validate_frame(&frame)) {
            uart_flash_process_frame(&frame);
            return true;
        } else {
            // Send error response for invalid frame
            uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_CHECKSUM, frame.header.sequence);
            return true;
        }
    }

    return false;
}

/**
 * Check if UART flash session is active
 * @return true if session is active
 */
bool uart_flash_is_active(void) {
    return g_flash_session.active;
}

/**
 * Send response to host
 * @param response Response code
 * @param error_code Error code (if response is NAK)
 * @param sequence Sequence number being acknowledged
 */
void uart_flash_send_response(uint8_t response, uint8_t error_code, uint16_t sequence) {
    UART_Flash_Response resp = {
        .response = response,
        .error_code = error_code,
        .sequence = sequence
    };

    uart_write_buffer_polled(BOOT_USART_MODULE, (uint8_t*)&resp, sizeof(resp));
}

/**
 * Reset UART flash session
 */
void uart_flash_reset_session(void) {
    memset(&g_flash_session, 0, sizeof(g_flash_session));
    g_rx_buffer_pos = 0;
    g_last_activity_time = timerHigh;
}

/**
 * Handle timeout in UART flash session
 */
void uart_flash_handle_timeout(void) {
    if (g_flash_session.active) {
        // Send timeout error
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_TIMEOUT, g_flash_session.last_sequence);
        uart_flash_reset_session();
    }
}

/**
 * Try to receive a complete frame from UART
 * @param frame Pointer to frame structure to fill
 * @return true if complete frame received, false otherwise
 */
static bool uart_flash_receive_frame(UART_Flash_Frame *frame) {
    // Read available bytes into buffer
    while (usart_is_rx_ready() && g_rx_buffer_pos < UART_FLASH_FRAME_SIZE) {
        g_rx_buffer[g_rx_buffer_pos++] = usart_getc();
    }

    // Check if we have enough data for a complete frame
    if (g_rx_buffer_pos < UART_FLASH_FRAME_SIZE) {
        return false;
    }

    // Look for frame start magic
    uint32_t start_pos = 0;
    bool found_start = false;

    for (uint32_t i = 0; i <= g_rx_buffer_pos - sizeof(uint32_t); i++) {
        uint32_t magic = *(uint32_t*)(g_rx_buffer + i);
        if (magic == UART_FLASH_MAGIC_START) {
            start_pos = i;
            found_start = true;
            break;
        }
    }

    if (!found_start) {
        // No start magic found, discard some data and continue
        if (g_rx_buffer_pos > UART_FLASH_FRAME_SIZE / 2) {
            memmove(g_rx_buffer, g_rx_buffer + UART_FLASH_FRAME_SIZE / 2,
                   g_rx_buffer_pos - UART_FLASH_FRAME_SIZE / 2);
            g_rx_buffer_pos -= UART_FLASH_FRAME_SIZE / 2;
        }
        return false;
    }

    // Check if we have a complete frame from start position
    if (g_rx_buffer_pos - start_pos < UART_FLASH_FRAME_SIZE) {
        // Move partial frame to beginning of buffer
        if (start_pos > 0) {
            memmove(g_rx_buffer, g_rx_buffer + start_pos, g_rx_buffer_pos - start_pos);
            g_rx_buffer_pos -= start_pos;
        }
        return false;
    }

    // Copy complete frame
    memcpy(frame, g_rx_buffer + start_pos, UART_FLASH_FRAME_SIZE);

    // Remove processed frame from buffer
    uint32_t remaining = g_rx_buffer_pos - start_pos - UART_FLASH_FRAME_SIZE;
    if (remaining > 0) {
        memmove(g_rx_buffer, g_rx_buffer + start_pos + UART_FLASH_FRAME_SIZE, remaining);
    }
    g_rx_buffer_pos = remaining;

    return true;
}

/**
 * Validate received frame
 * @param frame Pointer to frame to validate
 * @return true if frame is valid, false otherwise
 */
static bool uart_flash_validate_frame(const UART_Flash_Frame *frame) {
    // Check magic numbers
    if (frame->header.magic_start != UART_FLASH_MAGIC_START ||
        frame->magic_end != UART_FLASH_MAGIC_END) {
        return false;
    }

    // Check data length
    if (frame->header.length > UART_FLASH_BLOCK_SIZE) {
        return false;
    }

    // Validate header checksum (simple XOR of header bytes excluding checksum field)
    uint8_t calc_checksum = uart_flash_checksum((const uint8_t*)&frame->header,
                                               sizeof(UART_Flash_Header) - 1);
    if (calc_checksum != frame->header.checksum) {
        return false;
    }

    return true;
}

/**
 * Process validated frame
 * @param frame Pointer to validated frame
 */
static void uart_flash_process_frame(const UART_Flash_Frame *frame) {
    switch (frame->header.command) {
        case UART_FLASH_CMD_START:
            uart_flash_handle_start_command(frame);
            break;

        case UART_FLASH_CMD_DATA:
            uart_flash_handle_data_command(frame);
            break;

        case UART_FLASH_CMD_FINISH:
            uart_flash_handle_finish_command(frame);
            break;

        case UART_FLASH_CMD_RESET:
            uart_flash_handle_reset_command(frame);
            break;

        default:
            uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_CHECKSUM, frame->header.sequence);
            break;
    }
}

/**
 * Handle start command
 * @param frame Pointer to frame containing start command
 */
static void uart_flash_handle_start_command(const UART_Flash_Frame *frame) {
    // Reset session state
    uart_flash_reset_session();

    // Check if total size is reasonable
    if (frame->header.total_size == 0 || frame->header.total_size > UART_FLASH_MAX_SIZE) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_SIZE, frame->header.sequence);
        return;
    }

    // Activate session
    g_flash_session.active = true;
    g_flash_session.total_size = frame->header.total_size;
    g_flash_session.current_address = APP_START_ADDRESS;
    g_flash_session.start_time = timerHigh;
    g_flash_session.last_sequence = frame->header.sequence;

    // Send acknowledgment
    uart_flash_send_response(UART_FLASH_RESP_OK, UART_FLASH_ERR_NONE, frame->header.sequence);

    // Set LED color to indicate UART flashing
    RGBLED_set_color(COLOR_UART);
}

/**
 * Handle data command
 * @param frame Pointer to frame containing binary data
 */
static void uart_flash_handle_data_command(const UART_Flash_Frame *frame) {
    if (!g_flash_session.active) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_SEQUENCE, frame->header.sequence);
        return;
    }

    // Check sequence number (allow some flexibility for retransmissions)
    if (frame->header.sequence < g_flash_session.last_sequence ||
        frame->header.sequence > g_flash_session.last_sequence + 1) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_SEQUENCE, frame->header.sequence);
        return;
    }

    // Skip if this is a retransmission
    if (frame->header.sequence == g_flash_session.last_sequence) {
        uart_flash_send_response(UART_FLASH_RESP_OK, UART_FLASH_ERR_NONE, frame->header.sequence);
        return;
    }

    // Check if address is valid and in sequence
    if (frame->header.address != g_flash_session.current_address) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_ADDRESS, frame->header.sequence);
        return;
    }

    // Check if we're not exceeding total size
    if (g_flash_session.bytes_received + frame->header.length > g_flash_session.total_size) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_SIZE, frame->header.sequence);
        return;
    }

    // Write data directly to flash (simplified - write in 256-byte chunks)
    if (frame->header.length > 0) {
        flash_write_row((void*)frame->header.address, (void*)frame->data);
    }

    // Update session state
    g_flash_session.last_sequence = frame->header.sequence;
    g_flash_session.bytes_received += frame->header.length;
    g_flash_session.current_address += frame->header.length;

    // Send acknowledgment
    uart_flash_send_response(UART_FLASH_RESP_OK, UART_FLASH_ERR_NONE, frame->header.sequence);

    // Signal activity
    led_signal();
}

/**
 * Handle finish command
 * @param frame Pointer to frame containing finish command
 */
static void uart_flash_handle_finish_command(const UART_Flash_Frame *frame) {
    if (!g_flash_session.active) {
        uart_flash_send_response(UART_FLASH_RESP_ERROR, UART_FLASH_ERR_SEQUENCE, frame->header.sequence);
        return;
    }

    // Send acknowledgment
    uart_flash_send_response(UART_FLASH_RESP_OK, UART_FLASH_ERR_NONE, frame->header.sequence);

    // Check if all data was received
    if (g_flash_session.bytes_received >= g_flash_session.total_size) {
        // All data received, prepare to reset
        RGBLED_set_color(COLOR_LEAVE);
        delay(100);
        resetIntoApp();
    } else {
        // Not all data received, but finish requested
        uart_flash_reset_session();
    }
}

/**
 * Handle reset command
 * @param frame Pointer to frame containing reset command
 */
static void uart_flash_handle_reset_command(const UART_Flash_Frame *frame) {
    // Send acknowledgment
    uart_flash_send_response(UART_FLASH_RESP_OK, UART_FLASH_ERR_NONE, frame->header.sequence);

    // Reset session
    uart_flash_reset_session();

    // Reset into application
    RGBLED_set_color(COLOR_LEAVE);
    delay(100);
    resetIntoApp();
}

/**
 * Calculate simple XOR checksum
 * @param data Data buffer
 * @param length Data length
 * @return XOR checksum
 */
uint8_t uart_flash_checksum(const uint8_t *data, uint16_t length) {
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

#endif // USE_UART && USE_UART_UF2