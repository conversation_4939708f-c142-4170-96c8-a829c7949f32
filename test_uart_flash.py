#!/usr/bin/env python3
"""
UART Binary Flashing Test Client

This script demonstrates how to flash binary firmware files over UART using the
UART binary flash protocol implemented in the bootloader.

Usage:
    python3 test_uart_flash.py <serial_port> <binary_file>

Example:
    python3 test_uart_flash.py /dev/ttyUSB0 firmware.bin
    python3 test_uart_flash.py COM3 firmware.bin
"""

import sys
import time
import struct
import serial
import argparse
from pathlib import Path

# Protocol constants
UART_FLASH_MAGIC_START = 0x42494E46  # "BINF"
UART_FLASH_MAGIC_END = 0x454E4446    # "ENDF"
UART_FLASH_BLOCK_SIZE = 256
UART_FLASH_FRAME_SIZE = 276  # 16 + 256 + 4

# Commands
UART_FLASH_CMD_START = 0x01
UART_FLASH_CMD_DATA = 0x02
UART_FLASH_CMD_FINISH = 0x03
UART_FLASH_CMD_RESET = 0x04

# Response codes
UART_FLASH_RESP_OK = 0x06
UART_FLASH_RESP_ERROR = 0x15
UART_FLASH_RESP_READY = 0x52

# Error codes
UART_FLASH_ERR_NONE = 0x00
UART_FLASH_ERR_CHECKSUM = 0x01
UART_FLASH_ERR_ADDRESS = 0x02
UART_FLASH_ERR_SIZE = 0x03
UART_FLASH_ERR_SEQUENCE = 0x04
UART_FLASH_ERR_TIMEOUT = 0x05

def xor_checksum(data):
    """Calculate simple XOR checksum"""
    checksum = 0
    for byte in data:
        checksum ^= byte
    return checksum & 0xFF

def create_frame(command, sequence, address, data, total_size=0):
    """Create a UART binary flash protocol frame"""
    # Ensure data is exactly 256 bytes
    if len(data) < UART_FLASH_BLOCK_SIZE:
        data = data + b'\x00' * (UART_FLASH_BLOCK_SIZE - len(data))
    elif len(data) > UART_FLASH_BLOCK_SIZE:
        data = data[:UART_FLASH_BLOCK_SIZE]

    # Create header (without checksum)
    header_data = struct.pack('<LBHLHL',
                             UART_FLASH_MAGIC_START,
                             command,
                             sequence,
                             address,
                             len(data) if command == UART_FLASH_CMD_DATA else UART_FLASH_BLOCK_SIZE,
                             total_size)

    # Calculate header checksum
    header_checksum = xor_checksum(header_data)

    # Complete header with checksum
    header = header_data + struct.pack('<B', header_checksum)

    # Create footer
    footer = struct.pack('<L', UART_FLASH_MAGIC_END)

    # Combine frame
    frame = header + data + footer
    return frame

def read_response(ser, timeout=5.0):
    """Read response from bootloader"""
    start_time = time.time()
    response_data = b''

    while len(response_data) < 4 and (time.time() - start_time) < timeout:
        if ser.in_waiting > 0:
            response_data += ser.read(ser.in_waiting)
        time.sleep(0.01)

    if len(response_data) >= 4:
        response, error_code, sequence = struct.unpack('<BBH', response_data[:4])
        return response, error_code, sequence

    return None, None, None

def flash_binary_file(port, binary_file, baud_rate=115200, start_address=0x2000):
    """Flash binary file over UART"""
    print(f"Opening {port} at {baud_rate} baud...")

    try:
        ser = serial.Serial(port, baud_rate, timeout=1.0)
        time.sleep(0.1)  # Allow port to stabilize
    except Exception as e:
        print(f"Error opening serial port: {e}")
        return False

    try:
        # Read binary file
        print(f"Reading binary file: {binary_file}")
        with open(binary_file, 'rb') as f:
            binary_data = f.read()

        total_size = len(binary_data)
        num_blocks = (total_size + UART_FLASH_BLOCK_SIZE - 1) // UART_FLASH_BLOCK_SIZE
        print(f"Binary file size: {total_size} bytes ({num_blocks} blocks)")

        # Send start command
        print("Sending start command...")
        start_frame = create_frame(UART_FLASH_CMD_START, 0, start_address, b'\x00' * UART_FLASH_BLOCK_SIZE, total_size)
        ser.write(start_frame)

        response, error_code, sequence = read_response(ser)
        if response != UART_FLASH_RESP_OK:
            print(f"Start failed: response={response}, error={error_code}")
            return False

        print("Start successful")

        # Send all data blocks
        current_address = start_address
        for i in range(num_blocks):
            start_offset = i * UART_FLASH_BLOCK_SIZE
            end_offset = min(start_offset + UART_FLASH_BLOCK_SIZE, total_size)
            block_data = binary_data[start_offset:end_offset]

            print(f"Sending block {i+1}/{num_blocks} (address: 0x{current_address:08x})...")

            data_frame = create_frame(UART_FLASH_CMD_DATA, i+1, current_address, block_data)
            ser.write(data_frame)

            response, error_code, sequence = read_response(ser)
            if response != UART_FLASH_RESP_OK:
                print(f"Block {i+1} failed: response={response}, error={error_code}")
                return False

            current_address += len(block_data)

            # Show progress
            if (i + 1) % 10 == 0 or i == num_blocks - 1:
                progress = ((i + 1) * 100) // num_blocks
                print(f"Progress: {progress}% ({i+1}/{num_blocks} blocks)")

        # Send finish command
        print("Sending finish command...")
        finish_frame = create_frame(UART_FLASH_CMD_FINISH, num_blocks + 1, 0, b'\x00' * UART_FLASH_BLOCK_SIZE)
        ser.write(finish_frame)

        response, error_code, sequence = read_response(ser)
        if response != UART_FLASH_RESP_OK:
            print(f"Finish failed: response={response}, error={error_code}")
            return False

        print("Flashing completed successfully!")
        print("Device should reset into the new application...")

        return True

    except Exception as e:
        print(f"Error during flashing: {e}")
        return False

    finally:
        ser.close()

def main():
    parser = argparse.ArgumentParser(description='Flash binary file over UART')
    parser.add_argument('port', help='Serial port (e.g., /dev/ttyUSB0, COM3)')
    parser.add_argument('binary_file', help='Binary file to flash')
    parser.add_argument('--baud', type=int, default=115200, help='Baud rate (default: 115200)')
    parser.add_argument('--address', type=lambda x: int(x, 0), default=0x2000,
                       help='Start address (default: 0x2000)')

    args = parser.parse_args()

    if not Path(args.binary_file).exists():
        print(f"Error: Binary file '{args.binary_file}' not found")
        return 1

    success = flash_binary_file(args.port, args.binary_file, args.baud, args.address)
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())